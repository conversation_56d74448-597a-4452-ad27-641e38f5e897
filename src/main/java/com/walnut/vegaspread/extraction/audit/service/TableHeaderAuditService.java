package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.TableHeaderEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.audit.mapper.TableHeaderEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.TableHeaderAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class TableHeaderAuditService {

    private final GenericAuditService genericAuditService;
    private final TableHeaderEntityRollbackStrategy rollbackStrategy;

    public TableHeaderAuditService(GenericAuditService genericAuditService, 
                                  TableHeaderEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for TableHeaderEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The TableHeaderEntity to TableHeaderAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<TableHeaderAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, TableHeaderEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(TableHeaderEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific TableHeader ID.
     *
     * @param tableId       The table ID to get audits for
     * @param headerId      The header ID to get audits for
     * @param mapper        The TableHeaderEntity to TableHeaderAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<TableHeaderAuditDto.Response>> getAuditForTableHeaderId(
            Integer tableId, Short headerId, TableHeaderEntityAuditMapper mapper, EntityManager entityManager) {

        // Create filters for composite key
        var tableIdFilter = new AuditFilterDto(
                "tableHeaderPkId.tableId",
                tableId,
                AuditFilterDto.FilterOperation.EQUALS
        );
        
        var headerIdFilter = new AuditFilterDto(
                "tableHeaderPkId.headerId",
                headerId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(tableIdFilter, headerIdFilter),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback TableHeaderEntity to a specific traceId state.
     *
     * @param tableId       The table ID
     * @param headerId      The header ID
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     * @return The rolled back entity
     */
    @Transactional
    public TableHeaderEntity rollback(Integer tableId, Short headerId, String traceId, EntityManager entityManager) {
        // Create composite key for rollback
        var compositeKey = new Object[]{tableId, headerId};
        return genericAuditService.rollback(TableHeaderEntity.class, compositeKey, traceId, rollbackStrategy, entityManager);
    }
}
