package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class CoaDataEntityRollbackStrategy implements EntityRollbackStrategy<CoaDataEntity, Integer> {

    @Override
    @Transactional
    public CoaDataEntity findCurrentEntity(Integer id, EntityManager entityManager) {
        return entityManager.find(CoaDataEntity.class, id);
    }

    @Override
    @Transactional
    public CoaDataEntity saveEntity(CoaDataEntity entity, EntityManager entityManager) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional
    public void deleteEntity(Integer id, EntityManager entityManager) {
        CoaDataEntity entity = findCurrentEntity(id, entityManager);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    @Transactional
    public CoaDataEntity createNewEntity(CoaDataEntity auditedEntity, EntityManager entityManager) {
        // Create a new entity with the audited data
        CoaDataEntity newEntity = new CoaDataEntity();
        
        // Copy all fields from audited entity
        newEntity.setId(auditedEntity.getId());
        newEntity.setCoaId(auditedEntity.getCoaId());
        newEntity.setCoaScore(auditedEntity.getCoaScore());
        newEntity.setUseCoa(auditedEntity.getUseCoa());
        
        return entityManager.merge(newEntity);
    }

    @Override
    public String getEntityName() {
        return "CoaDataEntity";
    }

    @Override
    public Integer extractId(CoaDataEntity entity) {
        return entity.getId();
    }
}
