package com.walnut.vegaspread.extraction.audit.service;

import com.walnut.vegaspread.extraction.audit.rollback.ExtractedTableRowCoaDataJoinEntityRollbackStrategy;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.audit.mapper.ExtractedTableRowCoaDataJoinEntityAuditMapper;
import com.walnut.vegaspread.extraction.model.audit.ExtractedTableRowCoaDataJoinAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.util.List;

@ApplicationScoped
public class ExtractedTableRowCoaDataJoinAuditService {

    private final GenericAuditService genericAuditService;
    private final ExtractedTableRowCoaDataJoinEntityRollbackStrategy rollbackStrategy;

    public ExtractedTableRowCoaDataJoinAuditService(GenericAuditService genericAuditService, 
                                                   ExtractedTableRowCoaDataJoinEntityRollbackStrategy rollbackStrategy) {
        this.genericAuditService = genericAuditService;
        this.rollbackStrategy = rollbackStrategy;
    }

    /**
     * Get paginated audit data for ExtractedTableRowCoaDataJoinEntity with DTO mapping.
     *
     * @param request       The audit request
     * @param mapper        The ExtractedTableRowCoaDataJoinEntity to ExtractedTableRowCoaDataJoinAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<ExtractedTableRowCoaDataJoinAuditDto.Response>> getPaginatedAuditsAsDto(
            AuditRequestDto request, ExtractedTableRowCoaDataJoinEntityAuditMapper mapper, EntityManager entityManager) {
        return genericAuditService.getPaginatedAuditsAsDto(ExtractedTableRowCoaDataJoinEntity.class, request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific join entity.
     *
     * @param tableId       The table ID to get audits for
     * @param rowId         The row ID to get audits for
     * @param coaDataId     The coa data ID to get audits for
     * @param mapper        The ExtractedTableRowCoaDataJoinEntity to ExtractedTableRowCoaDataJoinAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<ExtractedTableRowCoaDataJoinAuditDto.Response>> getAuditForJoinEntity(
            Integer tableId, Short rowId, Integer coaDataId, ExtractedTableRowCoaDataJoinEntityAuditMapper mapper, EntityManager entityManager) {

        // Create filters for composite key
        var tableIdFilter = new AuditFilterDto(
                "extractedTableRowCoaDataPkId.tableId",
                tableId,
                AuditFilterDto.FilterOperation.EQUALS
        );
        
        var rowIdFilter = new AuditFilterDto(
                "extractedTableRowCoaDataPkId.rowId",
                rowId,
                AuditFilterDto.FilterOperation.EQUALS
        );
        
        var coaDataIdFilter = new AuditFilterDto(
                "extractedTableRowCoaDataPkId.coaDataId",
                coaDataId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(tableIdFilter, rowIdFilter, coaDataIdFilter),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Convenience method to get paginated audits for a specific table row.
     *
     * @param tableId       The table ID to get audits for
     * @param rowId         The row ID to get audits for
     * @param mapper        The ExtractedTableRowCoaDataJoinEntity to ExtractedTableRowCoaDataJoinAuditDto mapper
     * @param entityManager The entity manager
     * @return Paginated audit response with DTOs
     */
    public GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<ExtractedTableRowCoaDataJoinAuditDto.Response>> getAuditForTableRow(
            Integer tableId, Short rowId, ExtractedTableRowCoaDataJoinEntityAuditMapper mapper, EntityManager entityManager) {

        // Create filters for table and row
        var tableIdFilter = new AuditFilterDto(
                "extractedTableRowCoaDataPkId.tableId",
                tableId,
                AuditFilterDto.FilterOperation.EQUALS
        );
        
        var rowIdFilter = new AuditFilterDto(
                "extractedTableRowCoaDataPkId.rowId",
                rowId,
                AuditFilterDto.FilterOperation.EQUALS
        );

        var request = new AuditRequestDto(
                List.of(tableIdFilter, rowIdFilter),
                null,
                1,
                100
        );

        return getPaginatedAuditsAsDto(request, mapper, entityManager);
    }

    /**
     * Rollback ExtractedTableRowCoaDataJoinEntity to a specific traceId state.
     *
     * @param tableId       The table ID
     * @param rowId         The row ID
     * @param coaDataId     The coa data ID
     * @param traceId       The traceId to rollback to
     * @param entityManager The entity manager
     * @return The rolled back entity
     */
    @Transactional
    public ExtractedTableRowCoaDataJoinEntity rollback(Integer tableId, Short rowId, Integer coaDataId, String traceId, EntityManager entityManager) {
        // Create composite key for rollback
        var compositeKey = new Object[]{tableId, rowId, coaDataId};
        return genericAuditService.rollback(ExtractedTableRowCoaDataJoinEntity.class, compositeKey, traceId, rollbackStrategy, entityManager);
    }
}
