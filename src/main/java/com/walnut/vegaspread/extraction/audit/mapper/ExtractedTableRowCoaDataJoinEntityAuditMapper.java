package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.audit.ExtractedTableRowCoaDataJoinAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "cdi")
public interface ExtractedTableRowCoaDataJoinEntityAuditMapper extends BaseEntityMapper<ExtractedTableRowCoaDataJoinEntity, ExtractedTableRowCoaDataJoinAuditDto.Response> {

    @Named("mapExplainabilityId")
    static Integer mapExplainabilityId(CoaMappingEntity explainability) {
        return explainability == null ? null : explainability.getCoaMappingId();
    }

    @Override
    @Mapping(target = "tableId", source = "extractedTableRowCoaDataPkId.tableId")
    @Mapping(target = "rowId", source = "extractedTableRowCoaDataPkId.rowId")
    @Mapping(target = "coaDataId", source = "extractedTableRowCoaDataPkId.coaDataId")
    @Mapping(target = "explainabilityId", source = "explainability", qualifiedByName = "mapExplainabilityId")
    ExtractedTableRowCoaDataJoinAuditDto.Response toDto(ExtractedTableRowCoaDataJoinEntity entity);
}
