package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class TableHeaderEntityRollbackStrategy implements EntityRollbackStrategy<TableHeaderEntity, Object[]> {

    @Override
    @Transactional
    public TableHeaderEntity findCurrentEntity(Object[] compositeKey, EntityManager entityManager) {
        Integer tableId = (Integer) compositeKey[0];
        Short headerId = (Short) compositeKey[1];
        
        TableHeaderPkId pkId = new TableHeaderPkId();
        pkId.setTableId(tableId);
        pkId.setHeaderId(headerId);
        
        return entityManager.find(TableHeaderEntity.class, pkId);
    }

    @Override
    @Transactional
    public TableHeaderEntity saveEntity(TableHeaderEntity entity, EntityManager entityManager) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional
    public void deleteEntity(Object[] compositeKey, EntityManager entityManager) {
        TableHeaderEntity entity = findCurrentEntity(compositeKey, entityManager);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    @Transactional
    public TableHeaderEntity createNewEntity(TableHeaderEntity auditedEntity, EntityManager entityManager) {
        // Create a new entity with the audited data
        TableHeaderEntity newEntity = new TableHeaderEntity();
        
        // Copy all fields from audited entity
        newEntity.setTableHeaderPkId(auditedEntity.getTableHeaderPkId());
        newEntity.setText(auditedEntity.getText());
        newEntity.setBbox(auditedEntity.getBbox());
        newEntity.setScore(auditedEntity.getScore());
        newEntity.setPos(auditedEntity.getPos());
        newEntity.setLayoutBlock(auditedEntity.getLayoutBlock());
        
        return entityManager.merge(newEntity);
    }

    @Override
    public String getEntityName() {
        return "TableHeaderEntity";
    }

    @Override
    public Object[] extractId(TableHeaderEntity entity) {
        return new Object[]{
            entity.getTableHeaderPkId().getTableId(),
            entity.getTableHeaderPkId().getHeaderId()
        };
    }
}
