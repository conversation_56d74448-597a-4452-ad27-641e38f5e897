package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class ExtractedTableRowCoaDataJoinEntityRollbackStrategy implements EntityRollbackStrategy<ExtractedTableRowCoaDataJoinEntity, Object[]> {

    @Override
    @Transactional
    public ExtractedTableRowCoaDataJoinEntity findCurrentEntity(Object[] compositeKey, EntityManager entityManager) {
        Integer tableId = (Integer) compositeKey[0];
        Short rowId = (Short) compositeKey[1];
        Integer coaDataId = (Integer) compositeKey[2];
        
        ExtractedTableRowCoaDataPkId pkId = new ExtractedTableRowCoaDataPkId();
        pkId.setTableId(tableId);
        pkId.setRowId(rowId);
        pkId.setCoaDataId(coaDataId);
        
        return entityManager.find(ExtractedTableRowCoaDataJoinEntity.class, pkId);
    }

    @Override
    @Transactional
    public ExtractedTableRowCoaDataJoinEntity saveEntity(ExtractedTableRowCoaDataJoinEntity entity, EntityManager entityManager) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional
    public void deleteEntity(Object[] compositeKey, EntityManager entityManager) {
        ExtractedTableRowCoaDataJoinEntity entity = findCurrentEntity(compositeKey, entityManager);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    @Transactional
    public ExtractedTableRowCoaDataJoinEntity createNewEntity(ExtractedTableRowCoaDataJoinEntity auditedEntity, EntityManager entityManager) {
        // Create a new entity with the audited data
        ExtractedTableRowCoaDataJoinEntity newEntity = new ExtractedTableRowCoaDataJoinEntity();
        
        // Copy all fields from audited entity
        newEntity.setExtractedTableRowCoaDataPkId(auditedEntity.getExtractedTableRowCoaDataPkId());
        newEntity.setExplainability(auditedEntity.getExplainability());
        
        return entityManager.merge(newEntity);
    }

    @Override
    public String getEntityName() {
        return "ExtractedTableRowCoaDataJoinEntity";
    }

    @Override
    public Object[] extractId(ExtractedTableRowCoaDataJoinEntity entity) {
        return new Object[]{
            entity.getExtractedTableRowCoaDataPkId().getTableId(),
            entity.getExtractedTableRowCoaDataPkId().getRowId(),
            entity.getExtractedTableRowCoaDataPkId().getCoaDataId()
        };
    }
}
