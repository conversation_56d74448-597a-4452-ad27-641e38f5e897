package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class TableRowEntityRollbackStrategy implements EntityRollbackStrategy<TableRowEntity, Object[]> {

    @Override
    @Transactional
    public TableRowEntity findCurrentEntity(Object[] compositeKey, EntityManager entityManager) {
        Integer tableId = (Integer) compositeKey[0];
        Short rowId = (Short) compositeKey[1];
        
        TableRowPkId pkId = new TableRowPkId();
        pkId.setTableId(tableId);
        pkId.setRowId(rowId);
        
        return entityManager.find(TableRowEntity.class, pkId);
    }

    @Override
    @Transactional
    public TableRowEntity saveEntity(TableRowEntity entity, EntityManager entityManager) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional
    public void deleteEntity(Object[] compositeKey, EntityManager entityManager) {
        TableRowEntity entity = findCurrentEntity(compositeKey, entityManager);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    @Transactional
    public TableRowEntity createNewEntity(TableRowEntity auditedEntity, EntityManager entityManager) {
        // Create a new entity with the audited data
        TableRowEntity newEntity = new TableRowEntity();
        
        // Copy all fields from audited entity
        newEntity.setTableRowPkId(auditedEntity.getTableRowPkId());
        newEntity.setNtaTable(auditedEntity.getNtaTable());
        newEntity.setParentText(auditedEntity.getParentText());
        newEntity.setHeaderIds(auditedEntity.getHeaderIds());
        newEntity.setCellsText(auditedEntity.getCellsText());
        newEntity.setScore(auditedEntity.getScore());
        newEntity.setComment(auditedEntity.getComment());
        newEntity.setBbox(auditedEntity.getBbox());
        newEntity.setLayoutBlock(auditedEntity.getLayoutBlock());
        newEntity.setCoaData(auditedEntity.getCoaData());
        newEntity.setPos(auditedEntity.getPos());
        
        return entityManager.merge(newEntity);
    }

    @Override
    public String getEntityName() {
        return "TableRowEntity";
    }

    @Override
    public Object[] extractId(TableRowEntity entity) {
        return new Object[]{
            entity.getTableRowPkId().getTableId(),
            entity.getTableRowPkId().getRowId()
        };
    }
}
