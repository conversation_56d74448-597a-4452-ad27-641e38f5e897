package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.TableTagEntity;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class TableTagEntityRollbackStrategy implements EntityRollbackStrategy<TableTagEntity, Integer> {

    @Override
    @Transactional
    public TableTagEntity findCurrentEntity(Integer id, EntityManager entityManager) {
        return entityManager.find(TableTagEntity.class, id);
    }

    @Override
    @Transactional
    public TableTagEntity saveEntity(TableTagEntity entity, EntityManager entityManager) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional
    public void deleteEntity(Integer id, EntityManager entityManager) {
        TableTagEntity entity = findCurrentEntity(id, entityManager);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    @Transactional
    public TableTagEntity createNewEntity(TableTagEntity auditedEntity, EntityManager entityManager) {
        // Create a new entity with the audited data
        TableTagEntity newEntity = new TableTagEntity();
        
        // Copy all fields from audited entity (excluding audit metadata fields)
        newEntity.setId(auditedEntity.getId());
        newEntity.setTag(auditedEntity.getTag());
        // Note: createdBy, createdTime, lastModifiedBy, lastModifiedTime are @NotAudited
        // so they won't be restored from audit data
        
        return entityManager.merge(newEntity);
    }

    @Override
    public String getEntityName() {
        return "TableTagEntity";
    }

    @Override
    public Integer extractId(TableTagEntity entity) {
        return entity.getId();
    }
}
