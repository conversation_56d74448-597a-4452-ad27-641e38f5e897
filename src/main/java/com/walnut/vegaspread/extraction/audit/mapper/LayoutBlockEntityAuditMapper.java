package com.walnut.vegaspread.extraction.audit.mapper;

import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.model.audit.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "cdi")
public interface LayoutBlockEntityAuditMapper extends BaseEntityMapper<LayoutBlockEntity, LayoutBlockAuditDto.Response> {

    @Override
    LayoutBlockAuditDto.Response toDto(LayoutBlockEntity entity);
}
