package com.walnut.vegaspread.extraction.audit.controller;

import com.walnut.vegaspread.extraction.audit.service.*;
import com.walnut.vegaspread.extraction.audit.mapper.*;
import com.walnut.vegaspread.extraction.model.audit.*;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

@Path("/audit")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Extraction Audit", description = "Audit operations for extraction entities")
public class ExtractionAuditController {

    @Inject
    EntityManager entityManager;

    @Inject
    TableRowAuditService tableRowAuditService;
    @Inject
    TableRowEntityAuditMapper tableRowMapper;

    @Inject
    TableHeaderAuditService tableHeaderAuditService;
    @Inject
    TableHeaderEntityAuditMapper tableHeaderMapper;

    @Inject
    TableTagAuditService tableTagAuditService;
    @Inject
    TableTagEntityAuditMapper tableTagMapper;

    @Inject
    LayoutBlockAuditService layoutBlockAuditService;
    @Inject
    LayoutBlockEntityAuditMapper layoutBlockMapper;

    @Inject
    CoaDataAuditService coaDataAuditService;
    @Inject
    CoaDataEntityAuditMapper coaDataMapper;

    @Inject
    ExtractedTableRowCoaDataJoinAuditService extractedJoinAuditService;
    @Inject
    ExtractedTableRowCoaDataJoinEntityAuditMapper extractedJoinMapper;

    // TableRow Audit Endpoints
    @POST
    @Path("/table-row")
    @Operation(summary = "Get table row audit history")
    public Response getTableRowAudits(@Valid AuditRequestDto request) {
        var result = tableRowAuditService.getPaginatedAuditsAsDto(request, tableRowMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for specific table row")
    public Response getTableRowAudit(@PathParam("tableId") Integer tableId, @PathParam("rowId") Short rowId) {
        var result = tableRowAuditService.getAuditForTableRowId(tableId, rowId, tableRowMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-row/{tableId}/{rowId}/rollback/{traceId}")
    @Operation(summary = "Rollback table row to specific trace ID")
    public Response rollbackTableRow(@PathParam("tableId") Integer tableId, 
                                   @PathParam("rowId") Short rowId, 
                                   @PathParam("traceId") String traceId) {
        var result = tableRowAuditService.rollback(tableId, rowId, traceId, entityManager);
        return Response.ok(tableRowMapper.toDto(result)).build();
    }

    // TableHeader Audit Endpoints
    @POST
    @Path("/table-header")
    @Operation(summary = "Get table header audit history")
    public Response getTableHeaderAudits(@Valid AuditRequestDto request) {
        var result = tableHeaderAuditService.getPaginatedAuditsAsDto(request, tableHeaderMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-header/{tableId}/{headerId}")
    @Operation(summary = "Get audit history for specific table header")
    public Response getTableHeaderAudit(@PathParam("tableId") Integer tableId, @PathParam("headerId") Short headerId) {
        var result = tableHeaderAuditService.getAuditForTableHeaderId(tableId, headerId, tableHeaderMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-header/{tableId}/{headerId}/rollback/{traceId}")
    @Operation(summary = "Rollback table header to specific trace ID")
    public Response rollbackTableHeader(@PathParam("tableId") Integer tableId, 
                                      @PathParam("headerId") Short headerId, 
                                      @PathParam("traceId") String traceId) {
        var result = tableHeaderAuditService.rollback(tableId, headerId, traceId, entityManager);
        return Response.ok(tableHeaderMapper.toDto(result)).build();
    }

    // TableTag Audit Endpoints
    @POST
    @Path("/table-tag")
    @Operation(summary = "Get table tag audit history")
    public Response getTableTagAudits(@Valid AuditRequestDto request) {
        var result = tableTagAuditService.getPaginatedAuditsAsDto(request, tableTagMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-tag/{tagId}")
    @Operation(summary = "Get audit history for specific table tag")
    public Response getTableTagAudit(@PathParam("tagId") Integer tagId) {
        var result = tableTagAuditService.getAuditForTableTagId(tagId, tableTagMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-tag/{tagId}/rollback/{traceId}")
    @Operation(summary = "Rollback table tag to specific trace ID")
    public Response rollbackTableTag(@PathParam("tagId") Integer tagId, @PathParam("traceId") String traceId) {
        var result = tableTagAuditService.rollback(tagId, traceId, entityManager);
        return Response.ok(tableTagMapper.toDto(result)).build();
    }

    // LayoutBlock Audit Endpoints
    @POST
    @Path("/layout-block")
    @Operation(summary = "Get layout block audit history")
    public Response getLayoutBlockAudits(@Valid AuditRequestDto request) {
        var result = layoutBlockAuditService.getPaginatedAuditsAsDto(request, layoutBlockMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/layout-block/{blockId}")
    @Operation(summary = "Get audit history for specific layout block")
    public Response getLayoutBlockAudit(@PathParam("blockId") Integer blockId) {
        var result = layoutBlockAuditService.getAuditForLayoutBlockId(blockId, layoutBlockMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/layout-block/{blockId}/rollback/{traceId}")
    @Operation(summary = "Rollback layout block to specific trace ID")
    public Response rollbackLayoutBlock(@PathParam("blockId") Integer blockId, @PathParam("traceId") String traceId) {
        var result = layoutBlockAuditService.rollback(blockId, traceId, entityManager);
        return Response.ok(layoutBlockMapper.toDto(result)).build();
    }

    // CoaData Audit Endpoints
    @POST
    @Path("/coa-data")
    @Operation(summary = "Get COA data audit history")
    public Response getCoaDataAudits(@Valid AuditRequestDto request) {
        var result = coaDataAuditService.getPaginatedAuditsAsDto(request, coaDataMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/coa-data/{coaDataId}")
    @Operation(summary = "Get audit history for specific COA data")
    public Response getCoaDataAudit(@PathParam("coaDataId") Integer coaDataId) {
        var result = coaDataAuditService.getAuditForCoaDataId(coaDataId, coaDataMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/coa-data/{coaDataId}/rollback/{traceId}")
    @Operation(summary = "Rollback COA data to specific trace ID")
    public Response rollbackCoaData(@PathParam("coaDataId") Integer coaDataId, @PathParam("traceId") String traceId) {
        var result = coaDataAuditService.rollback(coaDataId, traceId, entityManager);
        return Response.ok(coaDataMapper.toDto(result)).build();
    }

    // ExtractedTableRowCoaDataJoin Audit Endpoints
    @POST
    @Path("/extracted-join")
    @Operation(summary = "Get extracted table row COA data join audit history")
    public Response getExtractedJoinAudits(@Valid AuditRequestDto request) {
        var result = extractedJoinAuditService.getPaginatedAuditsAsDto(request, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/extracted-join/{tableId}/{rowId}/{coaDataId}")
    @Operation(summary = "Get audit history for specific extracted join entity")
    public Response getExtractedJoinAudit(@PathParam("tableId") Integer tableId,
                                        @PathParam("rowId") Short rowId,
                                        @PathParam("coaDataId") Integer coaDataId) {
        var result = extractedJoinAuditService.getAuditForJoinEntity(tableId, rowId, coaDataId, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/extracted-join/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for all join entities of a table row")
    public Response getExtractedJoinAuditForTableRow(@PathParam("tableId") Integer tableId, @PathParam("rowId") Short rowId) {
        var result = extractedJoinAuditService.getAuditForTableRow(tableId, rowId, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/extracted-join/{tableId}/{rowId}/{coaDataId}/rollback/{traceId}")
    @Operation(summary = "Rollback extracted join entity to specific trace ID")
    public Response rollbackExtractedJoin(@PathParam("tableId") Integer tableId,
                                        @PathParam("rowId") Short rowId,
                                        @PathParam("coaDataId") Integer coaDataId,
                                        @PathParam("traceId") String traceId) {
        var result = extractedJoinAuditService.rollback(tableId, rowId, coaDataId, traceId, entityManager);
        return Response.ok(extractedJoinMapper.toDto(result)).build();
    }
}
