package com.walnut.vegaspread.extraction.audit.controller;

import com.walnut.vegaspread.extraction.audit.service.*;
import com.walnut.vegaspread.extraction.audit.mapper.*;
import com.walnut.vegaspread.extraction.model.audit.*;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

@Path("/audit")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Extraction Audit", description = "Audit operations for extraction entities")
public class ExtractionAuditController {

    @Inject
    EntityManager entityManager;

    @Inject
    TableRowAuditService tableRowAuditService;
    @Inject
    TableRowEntityAuditMapper tableRowMapper;

    @Inject
    TableHeaderAuditService tableHeaderAuditService;
    @Inject
    TableHeaderEntityAuditMapper tableHeaderMapper;

    @Inject
    TableTagAuditService tableTagAuditService;
    @Inject
    TableTagEntityAuditMapper tableTagMapper;

    @Inject
    LayoutBlockAuditService layoutBlockAuditService;
    @Inject
    LayoutBlockEntityAuditMapper layoutBlockMapper;

    @Inject
    CoaDataAuditService coaDataAuditService;
    @Inject
    CoaDataEntityAuditMapper coaDataMapper;

    @Inject
    ExtractedTableRowCoaDataJoinAuditService extractedJoinAuditService;
    @Inject
    ExtractedTableRowCoaDataJoinEntityAuditMapper extractedJoinMapper;

    // TableRow Audit Endpoints
    @POST
    @Path("/table-row")
    @Operation(summary = "Get table row audit history")
    public Response getTableRowAudits(@Valid AuditRequestDto request) {
        var result = tableRowAuditService.getPaginatedAuditsAsDto(request, tableRowMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for specific table row")
    public Response getTableRowAudit(@PathParam("tableId") Integer tableId, @PathParam("rowId") Short rowId) {
        var result = tableRowAuditService.getAuditForTableRowId(tableId, rowId, tableRowMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-row/rollback/{traceId}")
    @Operation(summary = "Rollback table row to specific trace ID")
    public Response rollbackTableRow(@PathParam("traceId") String traceId) {
        tableRowAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // TableHeader Audit Endpoints
    @POST
    @Path("/table-header")
    @Operation(summary = "Get table header audit history")
    public Response getTableHeaderAudits(@Valid AuditRequestDto request) {
        var result = tableHeaderAuditService.getPaginatedAuditsAsDto(request, tableHeaderMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-header/{tableId}/{headerId}")
    @Operation(summary = "Get audit history for specific table header")
    public Response getTableHeaderAudit(@PathParam("tableId") Integer tableId, @PathParam("headerId") Short headerId) {
        var result = tableHeaderAuditService.getAuditForTableHeaderId(tableId, headerId, tableHeaderMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-header/rollback/{traceId}")
    @Operation(summary = "Rollback table header to specific trace ID")
    public Response rollbackTableHeader(@PathParam("traceId") String traceId) {
        tableHeaderAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // TableTag Audit Endpoints
    @POST
    @Path("/table-tag")
    @Operation(summary = "Get table tag audit history")
    public Response getTableTagAudits(@Valid AuditRequestDto request) {
        var result = tableTagAuditService.getPaginatedAuditsAsDto(request, tableTagMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/table-tag/{tagId}")
    @Operation(summary = "Get audit history for specific table tag")
    public Response getTableTagAudit(@PathParam("tagId") Integer tagId) {
        var result = tableTagAuditService.getAuditForTableTagId(tagId, tableTagMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/table-tag/rollback/{traceId}")
    @Operation(summary = "Rollback table tag to specific trace ID")
    public Response rollbackTableTag(@PathParam("traceId") String traceId) {
        tableTagAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // LayoutBlock Audit Endpoints
    @POST
    @Path("/layout-block")
    @Operation(summary = "Get layout block audit history")
    public Response getLayoutBlockAudits(@Valid AuditRequestDto request) {
        var result = layoutBlockAuditService.getPaginatedAuditsAsDto(request, layoutBlockMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/layout-block/{blockId}")
    @Operation(summary = "Get audit history for specific layout block")
    public Response getLayoutBlockAudit(@PathParam("blockId") Integer blockId) {
        var result = layoutBlockAuditService.getAuditForLayoutBlockId(blockId, layoutBlockMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/layout-block/rollback/{traceId}")
    @Operation(summary = "Rollback layout block to specific trace ID")
    public Response rollbackLayoutBlock(@PathParam("traceId") String traceId) {
        layoutBlockAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // CoaData Audit Endpoints
    @POST
    @Path("/coa-data")
    @Operation(summary = "Get COA data audit history")
    public Response getCoaDataAudits(@Valid AuditRequestDto request) {
        var result = coaDataAuditService.getPaginatedAuditsAsDto(request, coaDataMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/coa-data/{coaDataId}")
    @Operation(summary = "Get audit history for specific COA data")
    public Response getCoaDataAudit(@PathParam("coaDataId") Integer coaDataId) {
        var result = coaDataAuditService.getAuditForCoaDataId(coaDataId, coaDataMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/coa-data/rollback/{traceId}")
    @Operation(summary = "Rollback COA data to specific trace ID")
    public Response rollbackCoaData(@PathParam("traceId") String traceId) {
        coaDataAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }

    // ExtractedTableRowCoaDataJoin Audit Endpoints
    @POST
    @Path("/extracted-join")
    @Operation(summary = "Get extracted table row COA data join audit history")
    public Response getExtractedJoinAudits(@Valid AuditRequestDto request) {
        var result = extractedJoinAuditService.getPaginatedAuditsAsDto(request, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/extracted-join/{tableId}/{rowId}/{coaDataId}")
    @Operation(summary = "Get audit history for specific extracted join entity")
    public Response getExtractedJoinAudit(@PathParam("tableId") Integer tableId,
                                        @PathParam("rowId") Short rowId,
                                        @PathParam("coaDataId") Integer coaDataId) {
        var result = extractedJoinAuditService.getAuditForJoinEntity(tableId, rowId, coaDataId, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @GET
    @Path("/extracted-join/table-row/{tableId}/{rowId}")
    @Operation(summary = "Get audit history for all join entities of a table row")
    public Response getExtractedJoinAuditForTableRow(@PathParam("tableId") Integer tableId, @PathParam("rowId") Short rowId) {
        var result = extractedJoinAuditService.getAuditForTableRow(tableId, rowId, extractedJoinMapper, entityManager);
        return Response.ok(result).build();
    }

    @POST
    @Path("/extracted-join/rollback/{traceId}")
    @Operation(summary = "Rollback extracted join entity to specific trace ID")
    public Response rollbackExtractedJoin(@PathParam("traceId") String traceId) {
        extractedJoinAuditService.rollback(traceId, entityManager);
        return Response.ok().build();
    }
}
