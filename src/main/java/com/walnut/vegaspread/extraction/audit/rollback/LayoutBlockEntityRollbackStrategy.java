package com.walnut.vegaspread.extraction.audit.rollback;

import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.common.service.audit.envers.EntityRollbackStrategy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class LayoutBlockEntityRollbackStrategy implements EntityRollbackStrategy<LayoutBlockEntity, Integer> {

    @Override
    @Transactional
    public LayoutBlockEntity findCurrentEntity(Integer blockId, EntityManager entityManager) {
        return entityManager.find(LayoutBlockEntity.class, blockId);
    }

    @Override
    @Transactional
    public LayoutBlockEntity saveEntity(LayoutBlockEntity entity, EntityManager entityManager) {
        return entityManager.merge(entity);
    }

    @Override
    @Transactional
    public void deleteEntity(Integer blockId, EntityManager entityManager) {
        LayoutBlockEntity entity = findCurrentEntity(blockId, entityManager);
        if (entity != null) {
            entityManager.remove(entity);
        }
    }

    @Override
    @Transactional
    public LayoutBlockEntity createNewEntity(LayoutBlockEntity auditedEntity, EntityManager entityManager) {
        // Create a new entity with the audited data
        LayoutBlockEntity newEntity = new LayoutBlockEntity();
        
        // Copy all fields from audited entity
        newEntity.setBlockId(auditedEntity.getBlockId());
        newEntity.setDocId(auditedEntity.getDocId());
        newEntity.setPageNum(auditedEntity.getPageNum());
        newEntity.setBlockType(auditedEntity.getBlockType());
        newEntity.setTag(auditedEntity.getTag());
        newEntity.setComment(auditedEntity.getComment());
        newEntity.setBbox(auditedEntity.getBbox());
        newEntity.setScore(auditedEntity.getScore());
        newEntity.setTagExplainabilityId(auditedEntity.getTagExplainabilityId());
        
        // Note: tableHeaders and tableRows are @NotAudited to avoid cycles
        // They will need to be handled separately if needed
        
        return entityManager.merge(newEntity);
    }

    @Override
    public String getEntityName() {
        return "LayoutBlockEntity";
    }

    @Override
    public Integer extractId(LayoutBlockEntity entity) {
        return entity.getBlockId();
    }
}
