package com.walnut.vegaspread.extraction.model.audit;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * Audit DTO for ExtractedTableRowCoaDataJoinEntity
 */
public interface ExtractedTableRowCoaDataJoinAuditDto {

    record Response(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            @NotNull Integer coaDataId,
            Integer explainabilityId
    ) implements Serializable {
    }

    record Create(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            @NotNull Integer coaDataId,
            Integer explainabilityId
    ) implements Serializable {
    }

    record Update(
            @NotNull Integer tableId,
            @NotNull Short rowId,
            @NotNull Integer coaDataId,
            Integer explainabilityId
    ) implements Serializable {
    }
}
