package com.walnut.vegaspread.extraction.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RevisionEntity;
import com.walnut.vegaspread.common.model.audit.envers.MetadataRevEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "layout_block")
@Audited
@RevisionEntity(MetadataRevEntity.class)
public class LayoutBlockEntity {

    public static final String BLOCK_ID_COL_NAME = "block_id";
    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String PAGE_NUM_COL_NAME = "page_num";
    public static final String BLOCK_TYPE_COL_NAME = "block_type";
    public static final String TAG_COL_NAME = "tag";
    public static final String SCORE_COL_NAME = "score";
    public static final String TABLE_HEADERS_MAPPED_COL_NAME = "layoutBlock";
    public static final String TABLE_ROWS_MAPPED_COL_NAME = "layoutBlock";
    public static final String COMMENT_COL_NAME = "comment";
    public static final String TAG_EXPLAINABILITY_ID_COL_NAME = "tag_explainability_id";
    public static final String TAG_FK_COL_NAME = "tag_id";

    @Column(name = TAG_EXPLAINABILITY_ID_COL_NAME, nullable = false)
    public Integer tagExplainabilityId;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = BLOCK_ID_COL_NAME, nullable = false)
    private Integer blockId;
    @Column(name = DOC_ID_COL_NAME, nullable = false)
    private UUID docId;
    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = PAGE_NUM_COL_NAME, nullable = false)
    private Short pageNum;
    @Enumerated(EnumType.STRING)
    @Column(name = BLOCK_TYPE_COL_NAME, nullable = false)
    private BlockTypeEnum blockType;
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = TAG_FK_COL_NAME, referencedColumnName = "id", nullable = false)
    private TableTagEntity tag;
    @Builder.Default
    @Column(name = COMMENT_COL_NAME, nullable = false)
    private String comment = StringUtils.EMPTY;
    @JsonUnwrapped
    @Embedded
    private Bbox bbox;
    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = SCORE_COL_NAME, nullable = false)
    private Byte score;
    @ToString.Exclude
    @OneToMany(mappedBy = TABLE_HEADERS_MAPPED_COL_NAME, cascade = CascadeType.ALL, orphanRemoval = true,
            fetch = FetchType.EAGER)
    @OrderBy("tableHeaderPkId.headerId ASC")
    @JsonManagedReference(value = "table-headers")
    @Builder.Default
    @NotAudited
    private List<TableHeaderEntity> tableHeaders = new ArrayList<>();
    @ToString.Exclude
    @OneToMany(mappedBy = TABLE_ROWS_MAPPED_COL_NAME, cascade = CascadeType.ALL, orphanRemoval = true,
            fetch = FetchType.EAGER)
    @OrderBy("tableRowPkId.rowId ASC")
    @JsonManagedReference(value = "table-rows")
    @Builder.Default
    @NotAudited
    private List<TableRowEntity> tableRows = new ArrayList<>();

    public LayoutBlockEntity(LayoutBlockEntity other, boolean includeReverseRelationship) {
        this.blockId = other.blockId;
        this.docId = other.docId;
        this.pageNum = other.pageNum;
        this.blockType = other.blockType;
        this.tag = other.tag;
        this.comment = other.comment;
        if (other.bbox != null) {
            this.bbox = other.bbox;
        }
        this.score = other.score;
        if (includeReverseRelationship && other.tableHeaders != null) {
            for (TableHeaderEntity tableHeader : other.tableHeaders) {
                TableHeaderEntity copy = new TableHeaderEntity(tableHeader);
                this.tableHeaders.add(copy);
            }
        }
        if (includeReverseRelationship && other.tableRows != null) {
            for (TableRowEntity tableRow : other.tableRows) {
                TableRowEntity copy = new TableRowEntity(tableRow);
                this.tableRows.add(copy);
            }
        }
        this.tagExplainabilityId = other.tagExplainabilityId;
    }
}
