package com.walnut.vegaspread.extraction.entity;

import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

@Setter
@Getter
@Entity
@NoArgsConstructor
@Table(name = ExtractedTableRowCoaDataJoinEntity.EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME)
@Audited
@RevisionEntity(MetadataRevEntity.class)
public class ExtractedTableRowCoaDataJoinEntity {
    public static final String EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME = "extracted_row_coa_data";

    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String ROW_ID_COL_NAME = "row_id";
    public static final String EXPLAINABILITY_ID_COL_NAME = "explainability_id";

    @EmbeddedId
    public ExtractedTableRowCoaDataPkId extractedTableRowCoaDataPkId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = EXPLAINABILITY_ID_COL_NAME,
            referencedColumnName = CoaMappingEntity.COA_MAPPING_ID_COL_NAME)
    public CoaMappingEntity explainability;

    public ExtractedTableRowCoaDataJoinEntity(TableRowPkId tableRowPkId, Integer coaDataId,
                                              CoaMappingEntity explainability) {
        this.explainability = explainability;
        this.extractedTableRowCoaDataPkId = new ExtractedTableRowCoaDataPkId(tableRowPkId, coaDataId);
    }
}
