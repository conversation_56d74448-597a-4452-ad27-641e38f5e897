package com.walnut.vegaspread.extraction.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = TableHeaderEntity.TABLE_NAME)
@Audited

public class TableHeaderEntity {

    public static final String TABLE_NAME = "extracted_table_header";
    public static final String TEXT_COL_NAME = "text";
    public static final String SCORE_COL_NAME = "score";
    public static final String LAYOUT_BLOCK_FOREIGN_KEY_COL_NAME = "table_id";
    public static final String POS_COL_NAME = "pos";

    @JsonUnwrapped
    @EmbeddedId
    private TableHeaderPkId tableHeaderPkId;

    @Column(name = TEXT_COL_NAME, nullable = false)
    private String text;

    @JsonUnwrapped
    @Embedded
    private Bbox bbox;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = SCORE_COL_NAME, nullable = false)
    private Byte score;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = POS_COL_NAME, nullable = false)
    private Integer pos;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = CascadeType.ALL)
    @JoinColumn(name = LAYOUT_BLOCK_FOREIGN_KEY_COL_NAME, referencedColumnName = LayoutBlockEntity.BLOCK_ID_COL_NAME,
            nullable = false)
    @JsonBackReference(value = "table-headers")
    @MapsId("tableId")
    private LayoutBlockEntity layoutBlock;

    public TableHeaderEntity(TableHeaderEntity other) {
        this.tableHeaderPkId = other.tableHeaderPkId;
        this.text = other.text;
        this.bbox = other.bbox;
        this.score = other.score;
        if (other.layoutBlock != null) {
            this.layoutBlock = new LayoutBlockEntity(other.layoutBlock, false);
        }
        this.pos = other.pos;
    }

    public ResponseDto.TableHeader toDto() {
        return new ResponseDto.TableHeader(this.tableHeaderPkId.getTableId(), this.tableHeaderPkId.getHeaderId(),
                this.text, this.bbox.getXMin(), this.bbox.getYMin(), this.bbox.getXMax(), this.bbox.getYMax(),
                this.score);
    }
}
