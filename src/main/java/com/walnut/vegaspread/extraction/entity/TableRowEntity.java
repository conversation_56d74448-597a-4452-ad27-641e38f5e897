package com.walnut.vegaspread.extraction.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.walnut.vegaspread.extraction.converter.IntListConverter;
import com.walnut.vegaspread.extraction.converter.StringListConverter;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Embedded;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RevisionEntity;
import com.walnut.vegaspread.common.model.audit.envers.MetadataRevEntity;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = TableRowEntity.TABLE_NAME)
@Audited
@RevisionEntity(MetadataRevEntity.class)
public class TableRowEntity {

    public static final String TABLE_NAME = "extracted_table_row";
    public static final String NTA_TABLE_FOREIGN_KEY_COL_NAME = "nta_table_id";
    public static final String COA_ID_COL_NAME = "coa_id";
    public static final String USE_COA_COL_NAME = "use_coa";
    public static final String COA_SCORE_COL_NAME = "coa_score";
    public static final String PARENT_TEXT_COL_NAME = "parent_text";
    public static final String HEADER_IDS_COL_NAME = "header_ids";
    public static final String CELLS_TEXT_COL_NAME = "cells_text";
    public static final String SCORE_COL_NAME = "score";
    public static final String LAYOUT_BLOCK_FOREIGN_KEY_COL_NAME = "table_id";
    public static final String COMMENT_COL_NAME = "comment";
    public static final String EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME = "extracted_row_coa_data";
    public static final String EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_TABLE_ID_COL_NAME = "table_id";
    public static final String EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_ROW_ID_COL_NAME = "row_id";
    public static final String EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_COA_ID_COL_NAME = "coa_data_id";
    public static final String POS_COL_NAME = "pos";

    @JsonUnwrapped
    @EmbeddedId
    private TableRowPkId tableRowPkId;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = NTA_TABLE_FOREIGN_KEY_COL_NAME, referencedColumnName = LayoutBlockEntity.BLOCK_ID_COL_NAME)
    private LayoutBlockEntity ntaTable;

    @Column(name = PARENT_TEXT_COL_NAME)
    private String parentText;

    @Convert(converter = IntListConverter.class)
    @Column(name = HEADER_IDS_COL_NAME)
    private List<Integer> headerIds;

    @Convert(converter = StringListConverter.class)
    @Column(name = CELLS_TEXT_COL_NAME, nullable = false)
    private List<String> cellsText;

    @Schema(type = SchemaType.INTEGER, examples = {"0"})
    @Column(name = SCORE_COL_NAME, nullable = false)
    private Byte score;

    @Column(name = COMMENT_COL_NAME)
    @Builder.Default
    private String comment = StringUtils.EMPTY;

    @JsonUnwrapped
    @Embedded
    private Bbox bbox;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = LAYOUT_BLOCK_FOREIGN_KEY_COL_NAME, referencedColumnName = LayoutBlockEntity.BLOCK_ID_COL_NAME,
            nullable = false)
    @JsonBackReference(value = "table-rows")
    @MapsId("tableId")
    private LayoutBlockEntity layoutBlock;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinTable(
            name = EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_NAME,
            joinColumns = {
                    @JoinColumn(name = EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_TABLE_ID_COL_NAME,
                            referencedColumnName = TableRowPkId.TABLE_ID_COL_NAME),
                    @JoinColumn(name = EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_ROW_ID_COL_NAME,
                            referencedColumnName = TableRowPkId.ROW_ID_COL_NAME)
            },
            inverseJoinColumns = @JoinColumn(name = EXTRACTED_TABLE_ROW_COA_DATA_JOIN_TABLE_COA_ID_COL_NAME,
                    referencedColumnName = CoaDataEntity.ID_COL_NAME)
    )
    private CoaDataEntity coaData;

    @Schema(type = SchemaType.INTEGER, examples = {"0"})
    @Column(name = POS_COL_NAME, nullable = false)
    private Integer pos;

    public TableRowEntity(TableRowEntity other) {
        this.tableRowPkId = other.tableRowPkId;
        if (other.ntaTable != null) {
            this.ntaTable = new LayoutBlockEntity(other.ntaTable, false);
        }
        if (other.coaData != null) {
            this.coaData = new CoaDataEntity(other.getCoaData());
        }
        this.parentText = other.parentText;
        this.headerIds = other.headerIds;
        this.cellsText = other.cellsText;
        this.score = other.score;
        this.comment = other.comment;
        if (other.bbox != null) {
            this.bbox = new Bbox(other.bbox);
        }
        if (other.layoutBlock != null) {
            this.layoutBlock = new LayoutBlockEntity(other.layoutBlock, false);
        }
        this.pos = other.pos;
    }
}
