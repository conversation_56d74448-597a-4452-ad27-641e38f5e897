package com.walnut.vegaspread.workflow.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.walnut.vegaspread.workflow.model.ClientConfig;
import com.walnut.vegaspread.workflow.model.ExternalCallbackDataDto;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

@ApplicationScoped
public class ExternalCallbackHttpClient {

    private static final Logger logger = Logger.getLogger(ExternalCallbackHttpClient.class);
    private final HttpClient httpClient = HttpClient.newHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final JsonWebToken jwt;

    public ExternalCallbackHttpClient(JsonWebToken jwt) {
        this.jwt = jwt;
    }

    public void sendRequest(ClientConfig config,
                            ExternalCallbackDataDto payload) throws IOException, InterruptedException {
        String jsonBody = objectMapper.writeValueAsString(payload);
        HttpRequest.BodyPublisher body = HttpRequest.BodyPublishers.ofString(jsonBody);

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(config.urlTemplate()))
                .timeout(Duration.ofSeconds(5))
                .method(config.method().name(), body);

        config.headers().forEach(requestBuilder::header);

        if (config.headers().keySet().stream().noneMatch(k -> k.equalsIgnoreCase("Content-Type"))) {
            requestBuilder.header("Content-Type", "application/json");
        }
        if (this.jwt != null) {
            String token = jwt.getRawToken();
            requestBuilder.header("Authorization", "Bearer " + token);
        }

        HttpRequest request = requestBuilder.build();
        logger.debugf("Sending request to %s with headers: %s and body: %s", request.uri(), request.headers(),
                jsonBody);
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() >= 400) {
            logger.errorf("Request failed: " + response.statusCode() + " - " + response.body());
        }
    }
}
