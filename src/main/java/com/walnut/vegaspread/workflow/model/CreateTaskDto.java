package com.walnut.vegaspread.workflow.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.workflow.validation.annotation.ValidMetadata;
import com.walnut.vegaspread.workflow.validation.model.CreateTaskValidatableMetadata;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.UUID;

@ValidMetadata
public record CreateTaskDto(@NotNull LocalDate period, @NotNull SpreadLevelEnum spreadLevel,
                            @Valid @NotNull Entity entityName, @Valid @NotNull Industry industry,
                            @Valid @NotNull Region region, @NotNull Boolean isDigital, @NotNull UUID docId,
                            @NotNull DenominationEnum fileDenomination, @NotNull DenominationEnum outputDenomination) {

    public record Entity(@JsonProperty("entityId") Integer id,
                         @JsonProperty("name") String name) implements CreateTaskValidatableMetadata {
    }

    public record Industry(@JsonProperty("industryId") Integer id,
                           @JsonProperty("industryName") String name) implements CreateTaskValidatableMetadata {
    }

    public record Region(@JsonProperty("regionId") Integer id,
                         @JsonProperty("regionName") String name) implements CreateTaskValidatableMetadata {
    }
}
