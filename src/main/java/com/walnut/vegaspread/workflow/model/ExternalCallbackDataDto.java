package com.walnut.vegaspread.workflow.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * DTO for EZEE callback payload
 */
@RegisterForReflection
public record ExternalCallbackDataDto(
        @JsonProperty("doc_id")
        String docId,

        @JsonProperty("application_id")
        String applicationId,

        @JsonProperty("status")
        String status
) {
}
