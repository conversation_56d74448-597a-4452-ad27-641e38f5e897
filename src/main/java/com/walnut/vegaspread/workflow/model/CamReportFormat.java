package com.walnut.vegaspread.workflow.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum CamReportFormat {
    MARKDOWN("text/markdown", "md"),
    DOCX("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx"),
    JSON("application/json", "json");
    private final String mimeType;
    private final String extension;

    CamReportFormat(String mimeType, String extension) {
        this.mimeType = mimeType;
        this.extension = extension;
    }

    /**
     * Get the file extension for the given mime type.
     */
    public static String getExtensionForMimeType(String mimeType) {
        for (CamReportFormat format : CamReportFormat.values()) {
            if (format.getMimeType().equals(mimeType)) {
                return format.getExtension();
            }
        }
        return null;
    }

    /**
     * Get all mime types.
     */
    public static List<String> getMimeTypes() {
        return Arrays.stream(CamReportFormat.values()).map(CamReportFormat::getMimeType).toList();
    }

    /**
     * Get all extensions.
     */
    public static List<String> getExtensions() {
        return Arrays.stream(CamReportFormat.values()).map(CamReportFormat::getExtension).toList();
    }
}
