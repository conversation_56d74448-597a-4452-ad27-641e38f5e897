package com.walnut.vegaspread.workflow.validation.annotation;

import com.walnut.vegaspread.workflow.validation.validator.ValidMetadataValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Constraint(validatedBy = ValidMetadataValidator.class)
public @interface ValidMetadata {

    String message() default "Metadata must have a non-null ID or a non-empty name";

    Class<? extends Payload>[] payload() default {};

    Class<?>[] groups() default {};
}