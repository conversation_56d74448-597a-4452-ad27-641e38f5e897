package com.walnut.vegaspread.workflow.validation.validator;

import com.walnut.vegaspread.workflow.model.CreateTaskDto;
import com.walnut.vegaspread.workflow.validation.annotation.ValidMetadata;
import com.walnut.vegaspread.workflow.validation.model.CreateTaskValidatableMetadata;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidMetadataValidator implements ConstraintValidator<ValidMetadata, CreateTaskDto> {

    @Override
    public boolean isValid(CreateTaskDto dto, ConstraintValidatorContext context) {
        boolean valid = true;

        if (isInvalidMetadata(dto.entityName())) {
            addViolation(context, "entityName");
            valid = false;
        }
        if (isInvalidMetadata(dto.industry())) {
            addViolation(context, "industry");
            valid = false;
        }
        if (isInvalidMetadata(dto.region())) {
            addViolation(context, "region");
            valid = false;
        }
        return valid;
    }

    private boolean isInvalidMetadata(CreateTaskValidatableMetadata metadata) {
        if (metadata == null) return true;
        if (metadata.id() != null) return false;
        return metadata.name() == null || metadata.name().isBlank();
    }

    private void addViolation(ConstraintValidatorContext context, String field) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(
                        "Invalid " + field + ": Id is null and name is null or empty"
                )
                .addPropertyNode(field)
                .addConstraintViolation();
    }
}
