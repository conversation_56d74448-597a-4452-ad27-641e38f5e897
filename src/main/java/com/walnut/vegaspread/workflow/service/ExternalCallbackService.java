package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.model.ClientConfig;
import com.walnut.vegaspread.workflow.model.ClientHttpMethod;
import com.walnut.vegaspread.workflow.model.ExternalCallbackDataDto;
import com.walnut.vegaspread.workflow.model.SupportedClients;
import com.walnut.vegaspread.workflow.utils.ExternalCallbackHttpClient;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@ApplicationScoped
public class ExternalCallbackService {

    private static final Logger logger = Logger.getLogger(ExternalCallbackService.class);
    private static final String ALL_USERS = "ALL_USERS";
    private final Map<String, SupportedClients> usernameToClientMap;
    private final Map<String, ClientConfig> clientNameConfigMap;

    private final ExternalCallbackHttpClient httpClient;

    public ExternalCallbackService(ExternalCallbackHttpClient httpClient) {
        this.httpClient = httpClient;
        this.usernameToClientMap = initUsernameToClientMap();
        this.clientNameConfigMap = initClientNameConfigMap();
    }

    private Map<String, String> extractHeadersForClient(String clientPrefix) {
        return System.getenv().entrySet().stream()
                .filter(e -> e.getKey().startsWith(clientPrefix + "_CALLBACK_HEADER_"))
                .collect(Collectors.toMap(
                        e -> e.getKey().substring((clientPrefix + "_CALLBACK_HEADER_").length()).replace('_', '-'),
                        Map.Entry::getValue
                ));
    }

    private Map<String, ClientConfig> initClientNameConfigMap() {
        Map<String, ClientConfig> map = new HashMap<>();

        List<String> supportedClientNames = EnumSet.allOf(SupportedClients.class).stream()
                .map(SupportedClients::getName)
                .toList();

        for (String clientName : supportedClientNames) {
            String finalClientName = clientName.toUpperCase(Locale.ROOT);
            String urlTemplate = System.getenv(finalClientName + "_CALLBACK_URL_TEMPLATE");
            String methodStr = System.getenv(finalClientName + "_CALLBACK_METHOD");

            if (urlTemplate == null) {
                logger.errorf("Missing urlTemplate environment variable for client: %s", clientName);
            }
            if (methodStr == null) {
                logger.errorf("Missing method environment variable for client: %s", clientName);
            }

            ClientHttpMethod method = methodStr == null ? null : ClientHttpMethod.valueOf(
                    methodStr.trim().toUpperCase(Locale.ROOT));
            Map<String, String> headers = extractHeadersForClient(finalClientName);

            map.put(finalClientName, new ClientConfig(urlTemplate, method, headers));
        }

        return map;
    }

    private Map<String, SupportedClients> initUsernameToClientMap() {
        Map<String, SupportedClients> map = new HashMap<>();
        for (SupportedClients client : SupportedClients.values()) {
            String username = System.getenv(client.getName() + "_CALLBACK_USERNAME");
            if (username == null || username.isBlank()) {
                logger.errorf("No username found for client: %s", client.getName());
                continue;
            }
            map.put(username.toLowerCase(), client);
        }
        return map;
    }

    public void route(String clientUsername, ExternalCallbackDataDto payload) {
        SupportedClients client = usernameToClientMap.get(clientUsername.toLowerCase());
        if (client == null) {
            logger.debugf("No client found for username: %s, trying ALL_USERS", clientUsername);
            client = usernameToClientMap.get(ALL_USERS.toLowerCase());
            if (client == null) {
                logger.debugf("Continuing without sending callback. No client found for username: %s", clientUsername);
                return;
            }
        }
        String clientName = client.getName();
        ClientConfig config = clientNameConfigMap.get(clientName);
        if (config == null) {
            logger.errorf("Continuing without sending callback. No client config found for client: %s", clientName);
            return;
        }
        if (config.urlTemplate() == null || config.method() == null) {
            logger.errorf("Continuing without sending callback. Missing url template or method for client: %s",
                    clientName);
            return;
        }
        try {
            String finalUrl = buildUrlFromTemplate(config.urlTemplate(), payload);
            httpClient.sendRequest(new ClientConfig(finalUrl, config.method(), config.headers()), payload);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Restore interrupted status
            logger.errorf("Request interrupted for client: %s", clientName, e);
        } catch (Exception e) {
            logger.errorf("Failed to send request for client: %s", clientName, e);
        }
    }

    public ExternalCallbackDataDto buildExternalCallbackData(UUID docId, String applicationId, StatusEnum status) {
        if (applicationId == null || applicationId.trim().isEmpty()) {
            logger.warnf("Application ID is null or empty for docId: %s, skipping external callback", docId);
            return null;
        }
        return new ExternalCallbackDataDto(docId.toString(), applicationId, status.toString());
    }

    private String buildUrlFromTemplate(String urlTemplate, ExternalCallbackDataDto payload) {
        return urlTemplate
                .replace("{docId}", payload.docId());
    }
}
