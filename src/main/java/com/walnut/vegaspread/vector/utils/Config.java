package com.walnut.vegaspread.vector.utils;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.ws.rs.core.Response;

public class Config {
    public static final String DEFAULT_SCHEMA = "quarkus.hibernate-orm.database.default-schema";

    private Config() {
        ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR, "Utility class");
    }
}
