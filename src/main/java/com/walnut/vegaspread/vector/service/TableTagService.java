package com.walnut.vegaspread.vector.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.vector.entity.TableTagEntity;
import com.walnut.vegaspread.vector.model.TableTagDto;
import com.walnut.vegaspread.vector.repository.TableTagRepositoryFactory;
import com.walnut.vegaspread.vector.repository.spi.TableTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class TableTagService {
    private static final Logger logger = Logger.getLogger(TableTagService.class);
    private final TableTagRepositoryInterface tableTagRepository;

    @Inject
    public TableTagService(TableTagRepositoryFactory tableTagRepositoryFactory) {
        this.tableTagRepository = tableTagRepositoryFactory.getTableTagRepository();
    }

    @Transactional
    public List<TableTagEntity> createTableTag(List<TableTagDto.Create> createDtos) {
        if (createDtos == null || createDtos.isEmpty()) {
            return Collections.emptyList();
        }
        if (createDtos.stream().anyMatch(dto -> dto.tagId() == 1)) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "One or more of the tag ids is 1. Tag id 1 is reserved for NA. Please use a different tag id");
            return null;
        }

        List<TableTagEntity> tableTagEntities = new ArrayList<>();
        for (TableTagDto.Create createDto : createDtos) {
            TableTagEntity tableTagEntity = new TableTagEntity();
            tableTagEntity.setTagId(createDto.tagId());
            tableTagEntity.setEmbedding(Utils.toFloatArray(createDto.embedding()));
            tableTagEntity.setBlockId(createDto.blockId());
            tableTagEntity.setDocId(createDto.docId());
            tableTagEntity.setLastModifiedTime(LocalDateTime.now());
            tableTagEntities.add(tableTagEntity);
        }
        tableTagRepository.persist(tableTagEntities);
        return tableTagEntities;
    }

    public TableTagDto.QueryResponse queryTableTag(TableTagDto.Query queryDto) {
        if (queryDto == null) {
            return new TableTagDto.QueryResponse(Collections.emptyList());
        }
        return tableTagRepository.findSimilar(queryDto);
    }

    @Transactional
    public long deleteTableTagDoc(TableTagDto.DeleteDoc deleteDocDto) {
        return tableTagRepository.deleteByDocId(deleteDocDto.docId());
    }

    @Transactional
    public long deleteTableTagBlock(TableTagDto.DeleteBlock deleteBlockDto) {
        return tableTagRepository.deleteByBlockId(deleteBlockDto.blockId());
    }
}
