package com.walnut.vegaspread.vector.service;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;
import com.walnut.vegaspread.vector.repository.FsTagRepositoryFactory;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class FsTagService {
    private static final Logger logger = Logger.getLogger(FsTagService.class);
    private final FsTagRepositoryInterface fsTagRepository;

    @Inject
    public FsTagService(FsTagRepositoryFactory fsTagRepositoryFactory) {
        this.fsTagRepository = fsTagRepositoryFactory.getFsTagRepository();
    }

    @Transactional
    public List<FsTagEntity> createFsTag(List<FsTagDto.Create> createDtos) {
        if (createDtos == null || createDtos.isEmpty()) {
            return Collections.emptyList();
        }
        if (createDtos.stream().anyMatch(dto -> dto.tagId() == 1)) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST,
                    "One or more of the tag ids is 1. Tag id 1 is reserved for NA. Please use a different tag id");
            return null;
        }
        List<FsTagEntity> fsTables = new ArrayList<>();
        for (FsTagDto.Create createDto : createDtos) {
            FsTagEntity fsTable = new FsTagEntity();
            fsTable.setBlockId(createDto.blockId());
            fsTable.setDocId(createDto.docId());
            fsTable.setTagId(createDto.tagId());
            fsTable.setEntityId(createDto.entityId());
            fsTable.setEntityName(createDto.entityName());
            fsTable.setSpreadLevel(createDto.spreadLevel());
            fsTable.setEmbedding(Utils.toFloatArray(createDto.embedding()));
            fsTable.setLastModifiedTime(LocalDateTime.now());
            fsTables.add(fsTable);
        }
        fsTagRepository.persist(fsTables);
        return fsTables;
    }

    public FsTagDto.QueryResponse queryFsTag(FsTagDto.Query queryDto) {
        List<FsTagEntity> fsTagEntities = fsTagRepository.findSimilar(queryDto);
        return new FsTagDto.QueryResponse(fsTagEntities.stream()
                .map(fsTagEntity -> new FsTagDto.QueryResponseItem(fsTagEntity.getBlockId(), fsTagEntity.getTagId(),
                        fsTagEntity.getEntityId(), fsTagEntity.getEntityName(), fsTagEntity.getSpreadLevel()))
                .toList());
    }

    @Transactional
    public long deleteFsTagDoc(FsTagDto.DeleteDoc deleteDocDto) {
        return fsTagRepository.deleteByDocId(deleteDocDto.docId());
    }

    @Transactional
    public long deleteFsTagBlock(FsTagDto.DeleteBlock deleteBlockDto) {
        return fsTagRepository.deleteByBlockId(deleteBlockDto.blockId());
    }
}
