package com.walnut.vegaspread.vector.repository;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

@ApplicationScoped
public class FsTagRepositoryFactory {
    private static final Logger logger = Logger.getLogger(FsTagRepositoryFactory.class);
    @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE)
    String cloudProviderType;
    Instance<FsTagRepositoryInterface> fsTagRepositories;

    public FsTagRepositoryFactory(Instance<FsTagRepositoryInterface> fsTagRepositories) {
        this.fsTagRepositories = fsTagRepositories;
    }

    public FsTagRepositoryInterface getFsTagRepository() {
        logger.debugf("CloudProviderType: " + cloudProviderType.toLowerCase());
        for (FsTagRepositoryInterface fsTagRepository : fsTagRepositories) {
            logger.debugf("FsTagRepository: " + fsTagRepository.getClass().getSimpleName());
            if (fsTagRepository.getClass().getSimpleName().toLowerCase().startsWith(cloudProviderType.toLowerCase())) {
                return fsTagRepository;
            }
        }
        ResponseException.throwResponseException(Response.Status.INTERNAL_SERVER_ERROR,
                "No cloud provider repository configured for type: " + cloudProviderType);
        return null;
    }
}
