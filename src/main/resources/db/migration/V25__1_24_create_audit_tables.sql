-- Create revision entity sequence
CREATE TABLE metadata_rev_entity_seq (
    next_val BIGINT
) ENGINE=InnoDB;

INSERT INTO metadata_rev_entity_seq VALUES (1);

-- Create revision entity metadata table
CREATE TABLE metadata_rev_entity (
    id INT NOT NULL,
    TIMESTAMP BIGINT NOT NULL,
    trace_id VARCHAR(255),
    username VARCHAR(255),
    PRIMARY KEY (id)
) ENGINE=InnoDB;

-- Create audit table for layout_block
CREATE TABLE layout_block_AUD (
    block_id INT NOT NULL,
    REV INT NOT NULL,
    REVTY<PERSON><PERSON> TINYINT,
    doc_id BINARY(16),
    page_num SMALLINT,
    block_type VARCHAR(255),
    tag VARCHAR(255),
    comment TEXT,
    x_min SMALLINT,
    x_max SMALLINT,
    y_min SMALLINT,
    y_max SMALLINT,
    score TINYINT,
    tag_explainability_id INT,
    PRIMARY KEY (block_id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;

-- Create audit table for extracted_table_header
CREATE TABLE extracted_table_header_AUD (
    table_id INT NOT NULL,
    header_id SMALLINT NOT NULL,
    REV INT NOT NULL,
    REVTYPE TINYINT,
    text TEXT,
    x_min SMALLINT,
    x_max SMALLINT,
    y_min SMALLINT,
    y_max SMALLINT,
    score TINYINT,
    pos INT,
    PRIMARY KEY (table_id, header_id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;

-- Create audit table for extracted_table_row
CREATE TABLE extracted_table_row_AUD (
    table_id INT NOT NULL,
    row_id SMALLINT NOT NULL,
    REV INT NOT NULL,
    REVTYPE TINYINT,
    parent_text TEXT,
    header_ids JSON,
    cells_text JSON,
    score TINYINT,
    comment TEXT,
    x_min SMALLINT,
    x_max SMALLINT,
    y_min SMALLINT,
    y_max SMALLINT,
    pos INT,
    PRIMARY KEY (table_id, row_id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;

-- Create audit table for table_tag
CREATE TABLE table_tag_AUD (
    id INT NOT NULL,
    REV INT NOT NULL,
    REVTYPE TINYINT,
    tag VARCHAR(255),
    PRIMARY KEY (id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;

-- Create audit table for coa_data
CREATE TABLE coa_data_AUD (
    id INT NOT NULL,
    REV INT NOT NULL,
    REVTYPE TINYINT,
    coa_id INT,
    coa_score TINYINT,
    use_coa BOOLEAN,
    PRIMARY KEY (id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;

-- Create audit table for extracted_row_coa_data (join table)
CREATE TABLE extracted_row_coa_data_AUD (
    table_id INT NOT NULL,
    row_id SMALLINT NOT NULL,
    coa_data_id INT NOT NULL,
    REV INT NOT NULL,
    REVTYPE TINYINT,
    explainability_id INT,
    PRIMARY KEY (table_id, row_id, coa_data_id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;

-- Create audit table for coa_mapping
CREATE TABLE coa_mapping_AUD (
    id INT NOT NULL,
    REV INT NOT NULL,
    REVTYPE TINYINT,
    table_id INT,
    row_id SMALLINT,
    doc_id BINARY(16),
    table_type_id INT,
    row_parent TEXT,
    text TEXT,
    fs_header TEXT,
    fs_text TEXT,
    coa_id INT,
    is_approved BOOLEAN,
    PRIMARY KEY (id, REV),
    FOREIGN KEY (REV) REFERENCES metadata_rev_entity(id)
) ENGINE=InnoDB;
